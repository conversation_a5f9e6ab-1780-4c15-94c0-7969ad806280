import { Toaster } from '@ttplatform/ui/components'
import { SlugProvider } from '~/src/context/slug-context'
import { SiteProvider } from '~/src/context/use-site-context'
import { getLocale } from '~/src/libs/data/cookies'
import { getAppAllMessages } from '~/src/localization/i18n'
import { dynamicActivate } from '~/src/localization/i18n-dynamic'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import LinguiClientProvider from '~/src/providers/lingui-client-provider'

type TProps = {
  children: React.ReactNode
}

export default async function Layout({ children }: TProps) {
  const locale = await getLocale()

  // Server side i18n
  const allMessages = await getAppAllMessages()
  const messages = allMessages[locale] ?? allMessages['en']
  if (!messages) {
    throw new Error(`No messages found for locale "${locale}" or fallback "en"`)
  }

  // Activate global i18n instance first
  await dynamicActivate(locale)

  // Then create local i18n instance for server components
  const i18n = initLingui(locale)
  i18n.load(locale, messages)
  i18n.activate(locale)

  return (
    <SiteProvider>
      <SlugProvider>
        <LinguiClientProvider initialLocale={locale} initialMessages={messages}>
          {children}
          <Toaster />
        </LinguiClientProvider>
      </SlugProvider>
    </SiteProvider>
  )
}
