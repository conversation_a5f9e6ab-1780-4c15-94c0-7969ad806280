import React from 'react'

interface MessengerIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
  className?: string
}

export const MessengerIcon: React.FC<MessengerIconProps> = ({
  size = 24,
  className,
}) => {
  const computedHeight = (size / 25) * 24
  return (
    <svg
      width={size}
      height={computedHeight}
      viewBox="0 0 25 24"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.4996 11.5C21.4996 16.1944 17.694 20 12.9996 20C11.9228 20 10.8928 19.7998 9.94478 19.4345C9.77145 19.3678 9.68478 19.3344 9.61586 19.3185C9.54807 19.3029 9.499 19.2963 9.42949 19.2937C9.35881 19.291 9.28127 19.299 9.12619 19.315L4.00517 19.8444C3.51692 19.8948 3.2728 19.9201 3.1288 19.8322C3.00337 19.7557 2.91794 19.6279 2.8952 19.4828C2.86909 19.3161 2.98575 19.1002 3.21906 18.6684L4.85472 15.6408C4.98942 15.3915 5.05677 15.2668 5.08728 15.1469C5.1174 15.0286 5.12469 14.9432 5.11505 14.8214C5.10529 14.6981 5.05119 14.5376 4.943 14.2166C4.65547 13.3636 4.49962 12.45 4.49962 11.5C4.49962 6.80558 8.3052 3 12.9996 3C17.694 3 21.4996 6.80558 21.4996 11.5Z"
        fill="#182230"
        stroke="#182230"
        strokeWidth="1.7"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
