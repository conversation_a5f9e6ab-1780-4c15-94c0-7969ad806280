import { MessageDescriptor, MessageOptions, setupI18n } from '@lingui/core'
import { setI18n } from '@lingui/react/server'

// Simple synchronous function that creates a basic i18n instance
// For server components, we'll use hardcoded strings instead of translations
export function initLingui(lang: string) {
  // Create a minimal i18n instance for server components
  const i18n = setupI18n({
    locale: lang,
    messages: { [lang]: {} },
  })

  // Override the _ function to match the expected signature
  // This prevents the "locale not activated" error
  i18n._ = ((descriptor: MessageDescriptor | string, values?: Record<string, unknown>, options?: MessageOptions): string => {
    // For server components, just return the English text
    // This is a temporary solution to avoid the async loading issue
    const translations: Record<string, string> = {
      'Products': 'Products',
      'Product Categories': 'Product Categories',
      'Services': 'Services',
      'News': 'News',
      'Promotions': 'Promotions',
      'Parts': 'Parts',
      'Careers': 'Careers',
      'No data': 'No data'
    }

    // Handle MessageDescriptor object
    if (typeof descriptor === 'object' && descriptor !== null) {
      const id = descriptor.id
      return translations[id] || id
    }

    // Handle string id
    return translations[descriptor] || descriptor
  }) as any

  i18n.activate(lang)
  setI18n(i18n)
  return i18n
}
