import type { Schema, Struct } from '@strapi/strapi'

export interface AdminApiToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_tokens'
  info: {
    description: ''
    displayName: 'Api Token'
    name: 'Api Token'
    pluralName: 'api-tokens'
    singularName: 'api-token'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }> &
      Schema.Attribute.DefaultTo<''>
    encryptedKey: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    expiresAt: Schema.Attribute.DateTime
    lastUsedAt: Schema.Attribute.DateTime
    lifespan: Schema.Attribute.BigInteger
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token'> &
      Schema.Attribute.Private
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    >
    publishedAt: Schema.Attribute.DateTime
    type: Schema.Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'read-only'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_token_permissions'
  info: {
    description: ''
    displayName: 'API Token Permission'
    name: 'API Token Permission'
    pluralName: 'api-token-permissions'
    singularName: 'api-token-permission'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    > &
      Schema.Attribute.Private
    publishedAt: Schema.Attribute.DateTime
    token: Schema.Attribute.Relation<'manyToOne', 'admin::api-token'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
  collectionName: 'admin_permissions'
  info: {
    description: ''
    displayName: 'Permission'
    name: 'Permission'
    pluralName: 'permissions'
    singularName: 'permission'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>
    conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::permission'> &
      Schema.Attribute.Private
    properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>
    publishedAt: Schema.Attribute.DateTime
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>
    subject: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface AdminRole extends Struct.CollectionTypeSchema {
  collectionName: 'admin_roles'
  info: {
    description: ''
    displayName: 'Role'
    name: 'Role'
    pluralName: 'roles'
    singularName: 'role'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.String
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::role'> &
      Schema.Attribute.Private
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::permission'>
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>
  }
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_tokens'
  info: {
    description: ''
    displayName: 'Transfer Token'
    name: 'Transfer Token'
    pluralName: 'transfer-tokens'
    singularName: 'transfer-token'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }> &
      Schema.Attribute.DefaultTo<''>
    expiresAt: Schema.Attribute.DateTime
    lastUsedAt: Schema.Attribute.DateTime
    lifespan: Schema.Attribute.BigInteger
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    >
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface AdminTransferTokenPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_token_permissions'
  info: {
    description: ''
    displayName: 'Transfer Token Permission'
    name: 'Transfer Token Permission'
    pluralName: 'transfer-token-permissions'
    singularName: 'transfer-token-permission'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    > &
      Schema.Attribute.Private
    publishedAt: Schema.Attribute.DateTime
    token: Schema.Attribute.Relation<'manyToOne', 'admin::transfer-token'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface AdminUser extends Struct.CollectionTypeSchema {
  collectionName: 'admin_users'
  info: {
    description: ''
    displayName: 'User'
    name: 'User'
    pluralName: 'users'
    singularName: 'user'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    blocked: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6
      }>
    firstname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    isActive: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>
    lastname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::user'> &
      Schema.Attribute.Private
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6
      }>
    preferedLanguage: Schema.Attribute.String
    publishedAt: Schema.Attribute.DateTime
    registrationToken: Schema.Attribute.String & Schema.Attribute.Private
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private
    roles: Schema.Attribute.Relation<'manyToMany', 'admin::role'> &
      Schema.Attribute.Private
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    username: Schema.Attribute.String
  }
}

export interface ApiArticleCategoryArticleCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'article_categories'
  info: {
    displayName: 'Article-Category'
    pluralName: 'article-categories'
    singularName: 'article-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    articles: Schema.Attribute.Relation<'oneToMany', 'api::article.article'>
    children: Schema.Attribute.Relation<
      'manyToOne',
      'api::article-category.article-category'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::article-category.article-category'
    >
    parent: Schema.Attribute.Relation<
      'oneToMany',
      'api::article-category.article-category'
    >
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'title'>
    title: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiArticleCommentArticleComment
  extends Struct.CollectionTypeSchema {
  collectionName: 'article_comments'
  info: {
    displayName: 'Article-Comment'
    pluralName: 'article-comments'
    singularName: 'article-comment'
  }
  options: {
    draftAndPublish: true
  }
  attributes: {
    article: Schema.Attribute.Relation<'manyToOne', 'api::article.article'>
    avatar: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>
    children: Schema.Attribute.Relation<
      'oneToMany',
      'api::article-comment.article-comment'
    >
    content: Schema.Attribute.Text
    count_like: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          min: 0
        },
        number
      >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    is_guess: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::article-comment.article-comment'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String
    parent: Schema.Attribute.Relation<
      'manyToOne',
      'api::article-comment.article-comment'
    >
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiArticleArticle extends Struct.CollectionTypeSchema {
  collectionName: 'articles'
  info: {
    description: ''
    displayName: 'Articles'
    pluralName: 'articles'
    singularName: 'article'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    article_comments: Schema.Attribute.Relation<
      'oneToMany',
      'api::article-comment.article-comment'
    >
    category: Schema.Attribute.Relation<
      'manyToOne',
      'api::article-category.article-category'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'elementals.media-video',
        'elementals.media-image',
        'dynamic-zone.related-products',
        'elementals.compare-images',
        'elementals.editor',
        'dynamic-zone.latest-promotions-section',
        'dynamic-zone.latest-news-section',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    featured: Schema.Attribute.Boolean &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::article.article'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'title'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    view: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }> &
      Schema.Attribute.SetMinMax<
        {
          min: 0
        },
        number
      >
  }
}

export interface ApiBlogPageBlogPage extends Struct.SingleTypeSchema {
  collectionName: 'blog_pages'
  info: {
    description: ''
    displayName: '/news'
    pluralName: 'blog-pages'
    singularName: 'blog-page'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    banner_ads: Schema.Attribute.Component<'elementals.banner-ads', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.related-products',
        'dynamic-zone.related-articles',
        'dynamic-zone.features',
        'dynamic-zone.features-section',
        'dynamic-zone.news-featured-section',
        'dynamic-zone.news-listing-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.widget',
        'dynamic-zone.media-text-section',
        'dynamic-zone.stacked-cards',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::blog-page.blog-page'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiBranchBranch extends Struct.CollectionTypeSchema {
  collectionName: 'branches'
  info: {
    displayName: 'Branches'
    pluralName: 'branches'
    singularName: 'branch'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    contact_items: Schema.Attribute.Component<'items.contact-item', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    images: Schema.Attribute.Media<'images', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::branch.branch'>
    map_url: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiBrandBrand extends Struct.CollectionTypeSchema {
  collectionName: 'brands'
  info: {
    displayName: 'Distributed Brands'
    pluralName: 'brands'
    singularName: 'brand'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.related-products',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.latest-news-section',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.accordion-section',
        'dynamic-zone.featured-promotions',
        'dynamic-zone.image-box-section',
        'dynamic-zone.info-block',
        'dynamic-zone.icon-box-list-section',
        'dynamic-zone.timeline-section',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::brand.brand'>
    logo: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    part_models: Schema.Attribute.Relation<
      'oneToMany',
      'api::part-model.part-model'
    >
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'name'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiCareerCategoryCareerCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'career_categories'
  info: {
    displayName: 'Career-Category'
    pluralName: 'career-categories'
    singularName: 'career-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    jobs: Schema.Attribute.Relation<'oneToMany', 'api::career.career'>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::career-category.career-category'
    >
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'title'>
    title: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiCareerPageCareerPage extends Struct.SingleTypeSchema {
  collectionName: 'career_pages'
  info: {
    displayName: '/careers'
    pluralName: 'career-pages'
    singularName: 'career-page'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.contact-us-section',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.job-listings-section',
        'dynamic-zone.info-block',
        'dynamic-zone.widget',
        'dynamic-zone.features-section',
        'dynamic-zone.related-articles',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::career-page.career-page'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'title'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiCareerSingleCareerSingle extends Struct.SingleTypeSchema {
  collectionName: 'career_singles'
  info: {
    displayName: '/career-single'
    pluralName: 'career-singles'
    singularName: 'career-single'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      ['dynamic-zone.form-subscribe', 'dynamic-zone.features-section']
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::career-single.career-single'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiCareerCareer extends Struct.CollectionTypeSchema {
  collectionName: 'careers'
  info: {
    displayName: 'Career'
    pluralName: 'careers'
    singularName: 'career'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    closing_date: Schema.Attribute.Date &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    form_job_applications: Schema.Attribute.Relation<
      'oneToMany',
      'api::form-job-application.form-job-application'
    >
    hiring_quantity: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    job_department: Schema.Attribute.Relation<
      'manyToOne',
      'api::career-category.career-category'
    >
    job_location: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    job_title: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    job_type: Schema.Attribute.Enumeration<['full-time', 'part-time']> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::career.career'>
    main_tasks: Schema.Attribute.Component<'items.job-info', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    requirements: Schema.Attribute.Component<'items.job-info', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'job_title'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiCategoryCategory extends Struct.CollectionTypeSchema {
  collectionName: 'categories'
  info: {
    description: ''
    displayName: 'Categories'
    pluralName: 'categories'
    singularName: 'category'
  }
  options: {
    draftAndPublish: false
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::category.category'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiFaqFaq extends Struct.CollectionTypeSchema {
  collectionName: 'faqs'
  info: {
    description: ''
    displayName: 'FAQ'
    pluralName: 'faqs'
    singularName: 'faq'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    answer: Schema.Attribute.Blocks &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::faq.faq'>
    publishedAt: Schema.Attribute.DateTime
    question: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    tag: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiFormJobApplicationFormJobApplication
  extends Struct.CollectionTypeSchema {
  collectionName: 'form_job_applications'
  info: {
    displayName: 'Form Job-Application'
    pluralName: 'form-job-applications'
    singularName: 'form-job-application'
  }
  options: {
    draftAndPublish: true
  }
  attributes: {
    career: Schema.Attribute.Relation<'manyToOne', 'api::career.career'>
    cover_letter_link: Schema.Attribute.String
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    email: Schema.Attribute.Email
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::form-job-application.form-job-application'
    > &
      Schema.Attribute.Private
    metadata: Schema.Attribute.Blocks
    name: Schema.Attribute.String & Schema.Attribute.Required
    phone: Schema.Attribute.String
    publishedAt: Schema.Attribute.DateTime
    resume_link: Schema.Attribute.String
    state: Schema.Attribute.Enumeration<['NEW', 'CONTACTED', 'REJECTED']> &
      Schema.Attribute.DefaultTo<'NEW'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiFormSubmissionFormSubmission
  extends Struct.CollectionTypeSchema {
  collectionName: 'form_submissions'
  info: {
    displayName: 'Form Contact-Submission'
    pluralName: 'form-submissions'
    singularName: 'form-submission'
  }
  options: {
    draftAndPublish: true
  }
  attributes: {
    company: Schema.Attribute.String
    content: Schema.Attribute.Text
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    email: Schema.Attribute.String
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::form-submission.form-submission'
    > &
      Schema.Attribute.Private
    metadata: Schema.Attribute.JSON
    name: Schema.Attribute.String & Schema.Attribute.Required
    phone: Schema.Attribute.String
    publishedAt: Schema.Attribute.DateTime
    state: Schema.Attribute.Enumeration<['new', 'answered', 'closed']> &
      Schema.Attribute.DefaultTo<'new'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiFormSubscribeFormSubscribe
  extends Struct.CollectionTypeSchema {
  collectionName: 'form_subscribes'
  info: {
    displayName: 'Form Subscribe'
    pluralName: 'form-subscribes'
    singularName: 'form-subscribe'
  }
  options: {
    draftAndPublish: true
  }
  attributes: {
    allow_send_email: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<true>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    email: Schema.Attribute.String & Schema.Attribute.Required
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::form-subscribe.form-subscribe'
    > &
      Schema.Attribute.Private
    metadata: Schema.Attribute.JSON
    name: Schema.Attribute.String
    publishedAt: Schema.Attribute.DateTime
    state: Schema.Attribute.Enumeration<['new', 'answered', 'closed']> &
      Schema.Attribute.DefaultTo<'new'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiGlobalGlobal extends Struct.SingleTypeSchema {
  collectionName: 'globals'
  info: {
    description: ''
    displayName: 'Global'
    pluralName: 'globals'
    singularName: 'global'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone_before_footer: Schema.Attribute.DynamicZone<
      ['dynamic-zone.features-section', 'dynamic-zone.form-subscribe']
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    footer: Schema.Attribute.Component<'global.footer', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    header: Schema.Attribute.Component<'global.header', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::global.global'>
    navbar: Schema.Attribute.Component<'global.navbar', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiHomeHome extends Struct.SingleTypeSchema {
  collectionName: 'homes'
  info: {
    displayName: '/home'
    pluralName: 'homes'
    singularName: 'home'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.testimonials-section',
        'dynamic-zone.related-products',
        'dynamic-zone.related-articles',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.locations-section',
        'dynamic-zone.latest-news-section',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.hero-slide-section',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.features',
        'dynamic-zone.featured-promotions',
        'dynamic-zone.executive-team-section',
        'dynamic-zone.accordion-section',
        'dynamic-zone.features-section',
        'dynamic-zone.support-center-section',
        'dynamic-zone.stats-section',
        'dynamic-zone.service-list-section',
        'dynamic-zone.partners-commitment-section',
        'dynamic-zone.job-listings-section',
        'dynamic-zone.contact-us-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.widget',
        'dynamic-zone.stacked-cards',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    heading: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::home.home'>
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'heading'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    subHeading: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiHrContactInformationHrContactInformation
  extends Struct.CollectionTypeSchema {
  collectionName: 'hr_contact_informations'
  info: {
    displayName: 'HR Contact Information'
    pluralName: 'hr-contact-informations'
    singularName: 'hr-contact-information'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    info: Schema.Attribute.Component<'items.contact-item', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::hr-contact-information.hr-contact-information'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiIndustrialSolutionCategoryIndustrialSolutionCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'industrial_solution_categories'
  info: {
    displayName: 'Industrial Solution-Category'
    pluralName: 'industrial-solution-categories'
    singularName: 'industrial-solution-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    applications: Schema.Attribute.Relation<
      'manyToMany',
      'api::solution-application.solution-application'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.latest-news-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.features-section',
        'dynamic-zone.featured-promotions',
        'dynamic-zone.accordion-section',
        'dynamic-zone.related-product-categories',
        'dynamic-zone.solutions-by-application',
        'dynamic-zone.info-block',
        'dynamic-zone.solution-applications-section',
        'dynamic-zone.related-industries',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    excerpt: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    industrial_solutions: Schema.Attribute.Relation<
      'oneToMany',
      'api::industrial-solution.industrial-solution'
    >
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::industrial-solution-category.industrial-solution-category'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'name'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiIndustrialSolutionIndustrialSolution
  extends Struct.CollectionTypeSchema {
  collectionName: 'industrial_solutions'
  info: {
    displayName: 'Industrial Solution'
    pluralName: 'industrial-solutions'
    singularName: 'industrial-solution'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    banner: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    category: Schema.Attribute.Relation<
      'manyToOne',
      'api::industrial-solution-category.industrial-solution-category'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.latest-news-section',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.features-section',
        'dynamic-zone.featured-promotions',
        'dynamic-zone.accordion-section',
        'dynamic-zone.testimonials-section',
        'dynamic-zone.video-section',
        'dynamic-zone.related-products',
        'dynamic-zone.media-text-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.related-product-categories',
        'elementals.media-video',
        'elementals.media-image',
        'dynamic-zone.info-block',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.news-related',
        'dynamic-zone.solution-media-gallery',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::industrial-solution.industrial-solution'
    >
    media_gallery: Schema.Attribute.Component<
      'elementals.media-gallery',
      false
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'name'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiLogoLogo extends Struct.CollectionTypeSchema {
  collectionName: 'logos'
  info: {
    description: ''
    displayName: 'Logos'
    pluralName: 'logos'
    singularName: 'logo'
  }
  options: {
    draftAndPublish: false
  }
  attributes: {
    company: Schema.Attribute.String
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::logo.logo'> &
      Schema.Attribute.Private
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPagePage extends Struct.CollectionTypeSchema {
  collectionName: 'pages'
  info: {
    description: ''
    displayName: 'Pages'
    pluralName: 'pages'
    singularName: 'page'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.testimonials-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.latest-news-section',
        'dynamic-zone.hero-slide-section',
        'dynamic-zone.accordion-section',
        'dynamic-zone.featured-promotions',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.video-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.executive-team-section',
        'dynamic-zone.locations-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.contact-us-section',
        'dynamic-zone.related-products',
        'dynamic-zone.stats-section',
        'dynamic-zone.features-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.job-listings-section',
        'dynamic-zone.related-articles',
        'dynamic-zone.service-list-section',
        'dynamic-zone.support-center-section',
        'dynamic-zone.partners-commitment-section',
        'dynamic-zone.solution-portfolio-section',
        'dynamic-zone.product-categories-section',
        'dynamic-zone.latest-promotions-section',
        'dynamic-zone.step-guide-section',
        'dynamic-zone.solutions-by-application',
        'dynamic-zone.related-product-categories',
        'dynamic-zone.icon-box-list-section',
        'dynamic-zone.solution-categories-section',
        'dynamic-zone.distributed-brands',
        'dynamic-zone.info-block',
        'dynamic-zone.timeline-section',
        'dynamic-zone.widget',
        'product-parts.product-part-categories',
        'products.specification-item',
        'products.products-recent-view',
        'products.products-by-category',
        'products.product-categories',
        'dynamic-zone.media-gallery-section',
        'dynamic-zone.policies-section',
        'dynamic-zone.toggle-card-slider',
        'dynamic-zone.stacked-cards',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::page.page'>
    page_slug: Schema.Attribute.UID<'title'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'title'> &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    template: Schema.Attribute.Enumeration<
      ['dynamic', 'home-page', 'blogs-page', 'products-page']
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false
        }
      }> &
      Schema.Attribute.DefaultTo<'dynamic'>
    title: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPartCategoryPartCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'part_categories'
  info: {
    displayName: 'Product-Part Categories'
    pluralName: 'part-categories'
    singularName: 'part-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    children: Schema.Attribute.Relation<
      'oneToMany',
      'api::part-category.part-category'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'product-parts.parts-by-category',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.icon-box-list-section',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    insights: Schema.Attribute.Component<
      'product-parts.insights-features-references-videos',
      false
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::part-category.part-category'
    >
    parent: Schema.Attribute.Relation<
      'manyToOne',
      'api::part-category.part-category'
    >
    parts: Schema.Attribute.Relation<'oneToMany', 'api::part.part'>
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'title'>
    title: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPartModelPartModel extends Struct.CollectionTypeSchema {
  collectionName: 'part_models'
  info: {
    displayName: 'Product-Part Model'
    pluralName: 'part-models'
    singularName: 'part-model'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    ad_image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    brand: Schema.Attribute.Relation<'manyToOne', 'api::brand.brand'>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.info-block',
        'product-parts.part-specifications',
        'product-parts.related-parts',
        'product-parts.compatible-products',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image_gallery: Schema.Attribute.Media<'images', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::part-model.part-model'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    part: Schema.Attribute.Relation<'manyToOne', 'api::part.part'>
    part_specification_values: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-specification-value.product-specification-value'
    >
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'name'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPartPagePartPage extends Struct.SingleTypeSchema {
  collectionName: 'part_pages'
  info: {
    displayName: '/part-page'
    pluralName: 'part-pages'
    singularName: 'part-page'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.stats-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.accordion-section',
        'product-parts.product-part-categories',
        'dynamic-zone.policies-section',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::part-page.part-page'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'title'>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPartPart extends Struct.CollectionTypeSchema {
  collectionName: 'parts'
  info: {
    displayName: 'Product-Parts'
    pluralName: 'parts'
    singularName: 'part'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    category: Schema.Attribute.Relation<
      'manyToOne',
      'api::part-category.part-category'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::part.part'>
    models: Schema.Attribute.Relation<'oneToMany', 'api::part-model.part-model'>
    name: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'name'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPolicyPolicy extends Struct.CollectionTypeSchema {
  collectionName: 'policies'
  info: {
    displayName: 'Policy'
    pluralName: 'policies'
    singularName: 'policy'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    content: Schema.Attribute.Blocks &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    excerpt: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::policy.policy'>
    publishedAt: Schema.Attribute.DateTime
    title: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductCategoriesPageProductCategoriesPage
  extends Struct.SingleTypeSchema {
  collectionName: 'product_categories_pages'
  info: {
    displayName: '/product-categories'
    pluralName: 'product-categories-pages'
    singularName: 'product-categories-page'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.solution-portfolio-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.accordion-section',
        'dynamic-zone.widget',
        'products.product-categories',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-categories-page.product-categories-page'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductCategorySingleProductCategorySingle
  extends Struct.SingleTypeSchema {
  collectionName: 'product_category_singles'
  info: {
    displayName: '/product-category-single'
    pluralName: 'product-category-singles'
    singularName: 'product-category-single'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.image-box-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.accordion-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.video-section',
        'dynamic-zone.widget',
        'products.products-by-category',
        'products.products-recent-view',
        'products.recently-viewed-products',
        'products.generator-selection-guide',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category-single.product-category-single'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductCategoryProductCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_categories'
  info: {
    displayName: 'Product Categories'
    pluralName: 'product-categories'
    singularName: 'product-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    children: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >
    content: Schema.Attribute.Blocks &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.media-text-section',
        'dynamic-zone.video-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.info-block',
        'products.generator-selection-guide',
        'dynamic-zone.solution-portfolio-section',
        'products.recently-viewed-products',
        'products.product-subcategories-section',
        'products.products-by-category',
        'dynamic-zone.toggle-card-slider',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    excerpt: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'videos' | 'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    parent: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-category.product-category'
    >
    products: Schema.Attribute.Relation<'oneToMany', 'api::product.product'>
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'name'> &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductModelProductModel
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_models'
  info: {
    displayName: 'Product Model'
    pluralName: 'product-models'
    singularName: 'product-model'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.media-text-section',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.features-section',
        'products.products-recent-view',
        'products.recently-viewed-products',
        'dynamic-zone.testimonials-section',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.accordion-section',
        'products.product-related-services',
        'products.product-media-gallery',
        'products.product-related-parts',
        'products.product-details-section',
        'dynamic-zone.icon-box-list-section',
        'dynamic-zone.toggle-card-slider',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    images: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-model.product-model'
    >
    media_gallery: Schema.Attribute.Component<
      'elementals.media-gallery',
      false
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>
    product_specification_values: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-specification-value.product-specification-value'
    >
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'name'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductPageProductPage extends Struct.SingleTypeSchema {
  collectionName: 'product_pages'
  info: {
    displayName: '/products'
    pluralName: 'product-pages'
    singularName: 'product-page'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.related-products',
        'dynamic-zone.related-articles',
        'dynamic-zone.features',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.product-categories-section',
        'dynamic-zone.widget',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-page.product-page'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'title'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    sub_heading: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductSingleProductSingle extends Struct.SingleTypeSchema {
  collectionName: 'product_singles'
  info: {
    displayName: '/product-single'
    pluralName: 'product-singles'
    singularName: 'product-single'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.page-hero-section',
        'dynamic-zone.features-section',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.hero-slide-section',
        'dynamic-zone.media-text-section',
        'products.generator-selection-guide',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-single.product-single'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductSpecificationValueProductSpecificationValue
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_specification_values'
  info: {
    displayName: 'Product Specification-Value'
    pluralName: 'product-specification-values'
    singularName: 'product-specification-value'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-specification-value.product-specification-value'
    >
    part_model: Schema.Attribute.Relation<
      'manyToOne',
      'api::part-model.part-model'
    >
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>
    product_model: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-model.product-model'
    >
    product_specification: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-specification.product-specification'
    >
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    value: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    value_in_us: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
  }
}

export interface ApiProductSpecificationProductSpecification
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_specifications'
  info: {
    displayName: 'Product Specification'
    pluralName: 'product-specifications'
    singularName: 'product-specification'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    children: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-specification.product-specification'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    is_root: Schema.Attribute.Boolean &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }> &
      Schema.Attribute.DefaultTo<false>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-specification.product-specification'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    parent: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-specification.product-specification'
    >
    product_specification_values: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-specification-value.product-specification-value'
    >
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'name'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    type: Schema.Attribute.Enumeration<['product', 'part']> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false
        }
      }> &
      Schema.Attribute.DefaultTo<'product'>
    unit: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    unit_in_us: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductTypeProductType extends Struct.CollectionTypeSchema {
  collectionName: 'product_types'
  info: {
    displayName: 'Product Type'
    pluralName: 'product-types'
    singularName: 'product-type'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-type.product-type'
    >
    name: Schema.Attribute.String & Schema.Attribute.Required
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiProductProduct extends Struct.CollectionTypeSchema {
  collectionName: 'products'
  info: {
    description: ''
    displayName: 'Products'
    pluralName: 'products'
    singularName: 'product'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.related-products',
        'dynamic-zone.video-section',
        'dynamic-zone.testimonials-section',
        'products.recently-viewed-products',
        'dynamic-zone.accordion-section',
        'products.product-media-gallery',
        'dynamic-zone.hover-expand-card-collection',
        'products.product-related-services',
        'products.product-related-parts',
        'products.product-comparison',
        'products.product-details-section',
        'dynamic-zone.icon-box-list-section',
        'dynamic-zone.toggle-card-slider',
      ]
    >
    featured: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    image: Schema.Attribute.Media<'images' | 'files' | 'videos'>
    image_gallery: Schema.Attribute.Media<'images', true>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product.product'
    >
    media_gallery: Schema.Attribute.Component<'elementals.media-gallery', false>
    name: Schema.Attribute.String
    product_category: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-category.product-category'
    >
    product_models: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-model.product-model'
    >
    product_specification_values: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-specification-value.product-specification-value'
    >
    product_types: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-type.product-type'
    >
    publishedAt: Schema.Attribute.DateTime
    related_promotion: Schema.Attribute.Relation<
      'manyToOne',
      'api::promotion.promotion'
    >
    related_services: Schema.Attribute.Relation<
      'manyToMany',
      'api::service.service'
    >
    slug: Schema.Attribute.UID<'name'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPromotionCategoryPromotionCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'promotion_categories'
  info: {
    displayName: 'Promotion-Category'
    pluralName: 'promotion-categories'
    singularName: 'promotion-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion-category.promotion-category'
    >
    promotions: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion.promotion'
    >
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'title'>
    title: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPromotionPagePromotionPage extends Struct.SingleTypeSchema {
  collectionName: 'promotion_pages'
  info: {
    displayName: '/promotions'
    pluralName: 'promotion-pages'
    singularName: 'promotion-page'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    banner_ads: Schema.Attribute.Component<'elementals.banner-ads', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.contact-us-section',
        'dynamic-zone.promotions-featured-section',
        'dynamic-zone.promotions-listing-section',
        'dynamic-zone.widget',
        'dynamic-zone.media-text-section',
        'dynamic-zone.stacked-cards',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion-page.promotion-page'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'title'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiPromotionPromotion extends Struct.CollectionTypeSchema {
  collectionName: 'promotions'
  info: {
    displayName: 'Promotions'
    pluralName: 'promotions'
    singularName: 'promotion'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    category: Schema.Attribute.Relation<
      'manyToOne',
      'api::promotion-category.promotion-category'
    >
    content: Schema.Attribute.Blocks &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.related-products',
        'dynamic-zone.latest-promotions-section',
        'dynamic-zone.video-section',
        'elementals.media-image',
        'elementals.media-video',
        'elementals.image-slider',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    featured: Schema.Attribute.Boolean &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion.promotion'
    >
    publishedAt: Schema.Attribute.DateTime
    related_products: Schema.Attribute.Relation<
      'oneToMany',
      'api::product.product'
    >
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'title'>
    title: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    valid_until: Schema.Attribute.Date &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
  }
}

export interface ApiRedirectionRedirection extends Struct.CollectionTypeSchema {
  collectionName: 'redirections'
  info: {
    displayName: 'Redirection'
    pluralName: 'redirections'
    singularName: 'redirection'
  }
  options: {
    draftAndPublish: true
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    destination: Schema.Attribute.String
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::redirection.redirection'
    > &
      Schema.Attribute.Private
    publishedAt: Schema.Attribute.DateTime
    source: Schema.Attribute.String
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiServiceCategoryServiceCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'service_categories'
  info: {
    displayName: 'Service Category'
    pluralName: 'service-categories'
    singularName: 'service-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::service-category.service-category'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiServiceService extends Struct.CollectionTypeSchema {
  collectionName: 'services'
  info: {
    displayName: 'Service List'
    pluralName: 'services'
    singularName: 'service'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    banner: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.solution-portfolio-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.related-products',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.image-box-section',
        'elementals.media-image',
        'dynamic-zone.other-services-section',
        'dynamic-zone.related-solutions-section',
        'dynamic-zone.info-block',
        'dynamic-zone.news-related',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::service.service'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    related_products: Schema.Attribute.Relation<
      'manyToMany',
      'api::product.product'
    >
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'name'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiServicesPageServicesPage extends Struct.SingleTypeSchema {
  collectionName: 'services_pages'
  info: {
    displayName: '/services'
    pluralName: 'services-pages'
    singularName: 'services-page'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.solution-portfolio-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.service-list-section',
        'dynamic-zone.icon-box-list-section',
        'dynamic-zone.info-block',
        'dynamic-zone.widget',
        'dynamic-zone.stacked-cards',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::services-page.services-page'
    >
    publishedAt: Schema.Attribute.DateTime
    seo: Schema.Attribute.Component<'shared.seo', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiServicesSinglePageServicesSinglePage
  extends Struct.SingleTypeSchema {
  collectionName: 'services_single_pages'
  info: {
    displayName: '/services-single'
    pluralName: 'services-single-pages'
    singularName: 'services-single-page'
  }
  options: {
    draftAndPublish: true
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.accordion-section',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.image-box-section',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.solution-portfolio-section',
        'dynamic-zone.video-section',
        'dynamic-zone.related-articles',
        'dynamic-zone.other-services-section',
        'dynamic-zone.info-block',
        'dynamic-zone.widget',
        'dynamic-zone.stacked-cards',
      ]
    >
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::services-single-page.services-single-page'
    > &
      Schema.Attribute.Private
    publishedAt: Schema.Attribute.DateTime
    title: Schema.Attribute.String
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiSolutionApplicationSolutionApplication
  extends Struct.CollectionTypeSchema {
  collectionName: 'solution_applications'
  info: {
    displayName: 'Solution Application'
    pluralName: 'solution-applications'
    singularName: 'solution-application'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    industrial_solutions: Schema.Attribute.Relation<
      'manyToMany',
      'api::industrial-solution-category.industrial-solution-category'
    >
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::solution-application.solution-application'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID<'name'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiSupportCenterCategorySupportCenterCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'support_center_categories'
  info: {
    displayName: 'Support-Center-Category'
    pluralName: 'support-center-categories'
    singularName: 'support-center-category'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::support-center-category.support-center-category'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    support_centers: Schema.Attribute.Relation<
      'oneToMany',
      'api::support-center.support-center'
    >
    type: Schema.Attribute.Enumeration<['CONTENT', 'VIDEO', 'DOCUMENT']> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }> &
      Schema.Attribute.DefaultTo<'CONTENT'>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiSupportCenterSupportCenter
  extends Struct.CollectionTypeSchema {
  collectionName: 'support_centers'
  info: {
    displayName: 'Support-Center'
    pluralName: 'support-centers'
    singularName: 'support-center'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    content: Schema.Attribute.Blocks &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    document_files: Schema.Attribute.Media<'files', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    document_link: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::support-center.support-center'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    slug: Schema.Attribute.UID<'name'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    support_center_category: Schema.Attribute.Relation<
      'manyToOne',
      'api::support-center-category.support-center-category'
    >
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    video: Schema.Attribute.Media<'videos'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    video_embed: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    video_thumbnail: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
  }
}

export interface ApiSurveyQuestionSurveyQuestion
  extends Struct.CollectionTypeSchema {
  collectionName: 'survey_questions'
  info: {
    displayName: 'Survey - Questions'
    pluralName: 'survey-questions'
    singularName: 'survey-question'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    answers: Schema.Attribute.Component<'items.answer', true> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    is_active: Schema.Attribute.Boolean &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }> &
      Schema.Attribute.DefaultTo<true>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::survey-question.survey-question'
    >
    main_question: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    question_type: Schema.Attribute.Enumeration<
      ['open-ended', 'dropdown', 'single_choice', 'multiple-choice', 'rating']
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    sub_question: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiSurveyStepSurveyStep extends Struct.CollectionTypeSchema {
  collectionName: 'survey_steps'
  info: {
    displayName: 'Survey - Steps'
    pluralName: 'survey-steps'
    singularName: 'survey-step'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.Blocks &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::survey-step.survey-step'
    >
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    show_questions: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }> &
      Schema.Attribute.DefaultTo<false>
    slug: Schema.Attribute.UID<'step_name'>
    step_name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiSurveySubmissionSurveySubmission
  extends Struct.CollectionTypeSchema {
  collectionName: 'survey_submissions'
  info: {
    displayName: 'Survey Submission'
    pluralName: 'survey-submissions'
    singularName: 'survey-submission'
  }
  options: {
    draftAndPublish: true
  }
  attributes: {
    company_name: Schema.Attribute.Text
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    email: Schema.Attribute.Email
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::survey-submission.survey-submission'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String
    phone_number: Schema.Attribute.String
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiTeamMemberTeamMember extends Struct.CollectionTypeSchema {
  collectionName: 'team_members'
  info: {
    displayName: 'Executive Team'
    pluralName: 'team-members'
    singularName: 'team-member'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::team-member.team-member'
    >
    name: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    name_prefix: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    position: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    publishedAt: Schema.Attribute.DateTime
    rank: Schema.Attribute.Integer &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface ApiTestimonialTestimonial extends Struct.CollectionTypeSchema {
  collectionName: 'testimonials'
  info: {
    description: ''
    displayName: 'Testimonials'
    pluralName: 'testimonials'
    singularName: 'testimonial'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::testimonial.testimonial'
    >
    publishedAt: Schema.Attribute.DateTime
    text: Schema.Attribute.Blocks &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    user: Schema.Attribute.Component<'shared.user', false> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
  }
}

export interface ApiWidgetWidget extends Struct.CollectionTypeSchema {
  collectionName: 'widgets'
  info: {
    displayName: 'Widget'
    pluralName: 'widgets'
    singularName: 'widget'
  }
  options: {
    draftAndPublish: true
  }
  pluginOptions: {
    i18n: {
      localized: true
    }
  }
  attributes: {
    adminLabel: Schema.Attribute.String &
      Schema.Attribute.Private &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    dynamic_zone: Schema.Attribute.DynamicZone<
      [
        'dynamic-zone.video-section',
        'dynamic-zone.timeline-section',
        'dynamic-zone.testimonials-section',
        'dynamic-zone.support-center-section',
        'dynamic-zone.step-guide-section',
        'dynamic-zone.stats-section',
        'dynamic-zone.solutions-by-application',
        'dynamic-zone.solution-portfolio-section',
        'dynamic-zone.solution-media-gallery',
        'dynamic-zone.solution-categories-section',
        'dynamic-zone.solution-applications-section',
        'dynamic-zone.service-list-section',
        'dynamic-zone.related-solutions-section',
        'dynamic-zone.related-products',
        'dynamic-zone.related-product-categories',
        'dynamic-zone.related-industries',
        'dynamic-zone.related-articles',
        'dynamic-zone.promotions-listing-section',
        'dynamic-zone.promotions-featured-section',
        'dynamic-zone.product-categories-section',
        'dynamic-zone.partners-commitment-section',
        'dynamic-zone.page-hero-section',
        'dynamic-zone.other-services-section',
        'dynamic-zone.news-listing-section',
        'dynamic-zone.news-featured-section',
        'dynamic-zone.media-text-section',
        'dynamic-zone.locations-section',
        'dynamic-zone.latest-promotions-section',
        'dynamic-zone.latest-news-section',
        'dynamic-zone.job-listings-section',
        'dynamic-zone.info-block',
        'dynamic-zone.image-slider-section',
        'dynamic-zone.image-box-section',
        'dynamic-zone.icon-box-list-section',
        'dynamic-zone.html',
        'dynamic-zone.hover-overlay-card-collection',
        'dynamic-zone.hover-expand-card-collection',
        'dynamic-zone.hero-slide-section',
        'dynamic-zone.form-subscribe',
        'dynamic-zone.features',
        'dynamic-zone.features-section',
        'dynamic-zone.featured-promotions',
        'dynamic-zone.executive-team-section',
        'dynamic-zone.distributed-brands',
        'dynamic-zone.contact-us-section',
        'dynamic-zone.accordion-section',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true
        }
      }>
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::widget.widget'>
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginContentReleasesRelease
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_releases'
  info: {
    displayName: 'Release'
    pluralName: 'releases'
    singularName: 'release'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    actions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    >
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String & Schema.Attribute.Required
    publishedAt: Schema.Attribute.DateTime
    releasedAt: Schema.Attribute.DateTime
    scheduledAt: Schema.Attribute.DateTime
    status: Schema.Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Schema.Attribute.Required
    timezone: Schema.Attribute.String
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginContentReleasesReleaseAction
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_release_actions'
  info: {
    displayName: 'Release Action'
    pluralName: 'release-actions'
    singularName: 'release-action'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    contentType: Schema.Attribute.String & Schema.Attribute.Required
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    entryDocumentId: Schema.Attribute.String
    isEntryValid: Schema.Attribute.Boolean
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    > &
      Schema.Attribute.Private
    publishedAt: Schema.Attribute.DateTime
    release: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::content-releases.release'
    >
    type: Schema.Attribute.Enumeration<['publish', 'unpublish']> &
      Schema.Attribute.Required
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
  collectionName: 'i18n_locale'
  info: {
    collectionName: 'locales'
    description: ''
    displayName: 'Locale'
    pluralName: 'locales'
    singularName: 'locale'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::i18n.locale'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String &
      Schema.Attribute.SetMinMax<
        {
          max: 50
          min: 1
        },
        number
      >
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginNavigationAudience extends Struct.CollectionTypeSchema {
  collectionName: 'audience'
  info: {
    displayName: 'Audience'
    name: 'audience'
    pluralName: 'audiences'
    singularName: 'audience'
  }
  options: {
    comment: 'Audience'
    draftAndPublish: false
    increments: true
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    key: Schema.Attribute.UID<'name'>
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::navigation.audience'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String & Schema.Attribute.Required
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginNavigationNavigation
  extends Struct.CollectionTypeSchema {
  collectionName: 'navigations'
  info: {
    displayName: 'Navigation'
    name: 'navigation'
    pluralName: 'navigations'
    singularName: 'navigation'
  }
  options: {
    comment: ''
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
    i18n: {
      localized: true
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    items: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::navigation.navigation-item'
    >
    locale: Schema.Attribute.String
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::navigation.navigation'
    >
    name: Schema.Attribute.Text & Schema.Attribute.Required
    publishedAt: Schema.Attribute.DateTime
    slug: Schema.Attribute.UID & Schema.Attribute.Required
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    visible: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
  }
}

export interface PluginNavigationNavigationItem
  extends Struct.CollectionTypeSchema {
  collectionName: 'navigations_items'
  info: {
    displayName: 'Navigation Item'
    name: 'navigation-item'
    pluralName: 'navigation-items'
    singularName: 'navigation-item'
  }
  options: {
    comment: 'Navigation Item'
    draftAndPublish: false
    increments: true
    timestamps: true
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
    i18n: {
      localized: false
    }
  }
  attributes: {
    additionalFields: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>
    audience: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::navigation.audience'
    >
    autoSync: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>
    collapsed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    externalPath: Schema.Attribute.Text
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::navigation.navigation-item'
    > &
      Schema.Attribute.Private
    master: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::navigation.navigation'
    >
    menuAttached: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    order: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>
    parent: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::navigation.navigation-item'
    >
    path: Schema.Attribute.Text
    publishedAt: Schema.Attribute.DateTime
    related: Schema.Attribute.Relation<'morphToMany'> &
      Schema.Attribute.Required
    title: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false
        }
      }>
    type: Schema.Attribute.Enumeration<['INTERNAL', 'EXTERNAL', 'WRAPPER']> &
      Schema.Attribute.DefaultTo<'INTERNAL'>
    uiRouterKey: Schema.Attribute.String
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginReviewWorkflowsWorkflow
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows'
  info: {
    description: ''
    displayName: 'Workflow'
    name: 'Workflow'
    pluralName: 'workflows'
    singularName: 'workflow'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    contentTypes: Schema.Attribute.JSON &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'[]'>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique
    publishedAt: Schema.Attribute.DateTime
    stageRequiredToPublish: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::review-workflows.workflow-stage'
    >
    stages: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    >
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginReviewWorkflowsWorkflowStage
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows_stages'
  info: {
    description: ''
    displayName: 'Stages'
    name: 'Workflow Stage'
    pluralName: 'workflow-stages'
    singularName: 'workflow-stage'
  }
  options: {
    draftAndPublish: false
    version: '1.1.0'
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    color: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#4945FF'>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String
    permissions: Schema.Attribute.Relation<'manyToMany', 'admin::permission'>
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    workflow: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::review-workflows.workflow'
    >
  }
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
  collectionName: 'files'
  info: {
    description: ''
    displayName: 'File'
    pluralName: 'files'
    singularName: 'file'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    alternativeText: Schema.Attribute.String
    caption: Schema.Attribute.String
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    ext: Schema.Attribute.String
    folder: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'> &
      Schema.Attribute.Private
    folderPath: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    formats: Schema.Attribute.JSON
    hash: Schema.Attribute.String & Schema.Attribute.Required
    height: Schema.Attribute.Integer
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.file'
    > &
      Schema.Attribute.Private
    mime: Schema.Attribute.String & Schema.Attribute.Required
    name: Schema.Attribute.String & Schema.Attribute.Required
    previewUrl: Schema.Attribute.String
    provider: Schema.Attribute.String & Schema.Attribute.Required
    provider_metadata: Schema.Attribute.JSON
    publishedAt: Schema.Attribute.DateTime
    related: Schema.Attribute.Relation<'morphToMany'>
    size: Schema.Attribute.Decimal & Schema.Attribute.Required
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    url: Schema.Attribute.String & Schema.Attribute.Required
    width: Schema.Attribute.Integer
  }
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
  collectionName: 'upload_folders'
  info: {
    displayName: 'Folder'
    pluralName: 'folders'
    singularName: 'folder'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    children: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    files: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'>
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.folder'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    parent: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'>
    path: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1
      }>
    pathId: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.Unique
    publishedAt: Schema.Attribute.DateTime
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginUsersPermissionsPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_permissions'
  info: {
    description: ''
    displayName: 'Permission'
    name: 'permission'
    pluralName: 'permissions'
    singularName: 'permission'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    action: Schema.Attribute.String & Schema.Attribute.Required
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    > &
      Schema.Attribute.Private
    publishedAt: Schema.Attribute.DateTime
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
  }
}

export interface PluginUsersPermissionsRole
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_roles'
  info: {
    description: ''
    displayName: 'Role'
    name: 'role'
    pluralName: 'roles'
    singularName: 'role'
  }
  options: {
    draftAndPublish: false
  }
  pluginOptions: {
    'content-manager': {
      visible: false
    }
    'content-type-builder': {
      visible: false
    }
  }
  attributes: {
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    description: Schema.Attribute.String
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    > &
      Schema.Attribute.Private
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3
      }>
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    >
    publishedAt: Schema.Attribute.DateTime
    type: Schema.Attribute.String & Schema.Attribute.Unique
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    users: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    >
  }
}

export interface PluginUsersPermissionsUser
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_users'
  info: {
    description: ''
    displayName: 'User'
    name: 'user'
    pluralName: 'users'
    singularName: 'user'
  }
  options: {
    draftAndPublish: false
    timestamps: true
  }
  attributes: {
    blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    confirmationToken: Schema.Attribute.String & Schema.Attribute.Private
    confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    createdAt: Schema.Attribute.DateTime
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6
      }>
    locale: Schema.Attribute.String & Schema.Attribute.Private
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    > &
      Schema.Attribute.Private
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6
      }>
    provider: Schema.Attribute.String
    publishedAt: Schema.Attribute.DateTime
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >
    updatedAt: Schema.Attribute.DateTime
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private
    username: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3
      }>
  }
}

declare module '@strapi/strapi' {
  export namespace Public {
    export interface ContentTypeSchemas {
      'admin::api-token': AdminApiToken
      'admin::api-token-permission': AdminApiTokenPermission
      'admin::permission': AdminPermission
      'admin::role': AdminRole
      'admin::transfer-token': AdminTransferToken
      'admin::transfer-token-permission': AdminTransferTokenPermission
      'admin::user': AdminUser
      'api::article-category.article-category': ApiArticleCategoryArticleCategory
      'api::article-comment.article-comment': ApiArticleCommentArticleComment
      'api::article.article': ApiArticleArticle
      'api::blog-page.blog-page': ApiBlogPageBlogPage
      'api::branch.branch': ApiBranchBranch
      'api::brand.brand': ApiBrandBrand
      'api::career-category.career-category': ApiCareerCategoryCareerCategory
      'api::career-page.career-page': ApiCareerPageCareerPage
      'api::career-single.career-single': ApiCareerSingleCareerSingle
      'api::career.career': ApiCareerCareer
      'api::category.category': ApiCategoryCategory
      'api::faq.faq': ApiFaqFaq
      'api::form-job-application.form-job-application': ApiFormJobApplicationFormJobApplication
      'api::form-submission.form-submission': ApiFormSubmissionFormSubmission
      'api::form-subscribe.form-subscribe': ApiFormSubscribeFormSubscribe
      'api::global.global': ApiGlobalGlobal
      'api::home.home': ApiHomeHome
      'api::hr-contact-information.hr-contact-information': ApiHrContactInformationHrContactInformation
      'api::industrial-solution-category.industrial-solution-category': ApiIndustrialSolutionCategoryIndustrialSolutionCategory
      'api::industrial-solution.industrial-solution': ApiIndustrialSolutionIndustrialSolution
      'api::logo.logo': ApiLogoLogo
      'api::page.page': ApiPagePage
      'api::part-category.part-category': ApiPartCategoryPartCategory
      'api::part-model.part-model': ApiPartModelPartModel
      'api::part-page.part-page': ApiPartPagePartPage
      'api::part.part': ApiPartPart
      'api::policy.policy': ApiPolicyPolicy
      'api::product-categories-page.product-categories-page': ApiProductCategoriesPageProductCategoriesPage
      'api::product-category-single.product-category-single': ApiProductCategorySingleProductCategorySingle
      'api::product-category.product-category': ApiProductCategoryProductCategory
      'api::product-model.product-model': ApiProductModelProductModel
      'api::product-page.product-page': ApiProductPageProductPage
      'api::product-single.product-single': ApiProductSingleProductSingle
      'api::product-specification-value.product-specification-value': ApiProductSpecificationValueProductSpecificationValue
      'api::product-specification.product-specification': ApiProductSpecificationProductSpecification
      'api::product-type.product-type': ApiProductTypeProductType
      'api::product.product': ApiProductProduct
      'api::promotion-category.promotion-category': ApiPromotionCategoryPromotionCategory
      'api::promotion-page.promotion-page': ApiPromotionPagePromotionPage
      'api::promotion.promotion': ApiPromotionPromotion
      'api::redirection.redirection': ApiRedirectionRedirection
      'api::service-category.service-category': ApiServiceCategoryServiceCategory
      'api::service.service': ApiServiceService
      'api::services-page.services-page': ApiServicesPageServicesPage
      'api::services-single-page.services-single-page': ApiServicesSinglePageServicesSinglePage
      'api::solution-application.solution-application': ApiSolutionApplicationSolutionApplication
      'api::support-center-category.support-center-category': ApiSupportCenterCategorySupportCenterCategory
      'api::support-center.support-center': ApiSupportCenterSupportCenter
      'api::survey-question.survey-question': ApiSurveyQuestionSurveyQuestion
      'api::survey-step.survey-step': ApiSurveyStepSurveyStep
      'api::survey-submission.survey-submission': ApiSurveySubmissionSurveySubmission
      'api::team-member.team-member': ApiTeamMemberTeamMember
      'api::testimonial.testimonial': ApiTestimonialTestimonial
      'api::widget.widget': ApiWidgetWidget
      'plugin::content-releases.release': PluginContentReleasesRelease
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction
      'plugin::i18n.locale': PluginI18NLocale
      'plugin::navigation.audience': PluginNavigationAudience
      'plugin::navigation.navigation': PluginNavigationNavigation
      'plugin::navigation.navigation-item': PluginNavigationNavigationItem
      'plugin::review-workflows.workflow': PluginReviewWorkflowsWorkflow
      'plugin::review-workflows.workflow-stage': PluginReviewWorkflowsWorkflowStage
      'plugin::upload.file': PluginUploadFile
      'plugin::upload.folder': PluginUploadFolder
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission
      'plugin::users-permissions.role': PluginUsersPermissionsRole
      'plugin::users-permissions.user': PluginUsersPermissionsUser
    }
  }
}
