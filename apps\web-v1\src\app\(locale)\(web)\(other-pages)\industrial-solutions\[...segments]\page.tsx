import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'

import { PageBreadcrumb } from '~/src/modules/layout'

//----------------------------------------------------------------------------------

export default async function SegmentsPage({
  params,
}: {
  params: Promise<{ segments: string[] }>
}) {
  const { segments } = await params

  const locale = await getLocale()

  // get category data
  const pageCategoryData = await fetchContentType({
    contentType: apiRoute.industrialSolutionCategories,
    params: {
      filters: {
        slug: segments?.[0],
        locale,
      },
    },
    spreadData: true,
  })

  // get industry data for segments.length === 2
  let industryData = null
  if (segments?.length === 2) {
    industryData = await fetchContentType({
      contentType: apiRoute.industrialSolutions,
      params: {
        filters: {
          slug: segments?.[1],
          locale,
        },
      },
      spreadData: true,
    })
  }

  const localizedCategorySlugs = pageCategoryData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.segments?.[0]

      return acc
    },
    { [locale]: segments?.[0] },
  )

  const localizedIndustrySlugs = industryData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug

      return acc
    },
    { [locale]: segments?.[1] },
  )

  return (
    <>
      {segments?.length && segments?.length === 1 ? (
        <>
          <ClientSlugHandler localizedSlugs={localizedCategorySlugs} />

          <PageBreadcrumb items={[{ title: pageCategoryData?.name }]} />

          <PageContent pageData={pageCategoryData} />
        </>
      ) : null}
      {segments?.length && segments?.length === 2 ? (
        <>
          <ClientSlugHandler localizedSlugs={localizedIndustrySlugs} />

          <PageBreadcrumb
            items={[
              { title: pageCategoryData?.name, href: '#' },
              { title: industryData?.name },
            ]}
          />

          <PageContent pageData={industryData} />
        </>
      ) : null}
    </>
  )
}
