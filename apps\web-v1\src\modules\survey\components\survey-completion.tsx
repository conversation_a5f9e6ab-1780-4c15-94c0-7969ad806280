'use client'

// Survey completion page component
import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { ArrowRight } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import HeadingSection from '~/src/components/renderer/heading-section'

interface SurveyCompletionProps {
  onGoHome?: () => void
  homeUrl?: string
}

// Heading data for completion page
const completionHeading = {
  type: 'default',
  heading: {
    text: 'HOÀN THÀNH KHẢO SÁT',
    styles: {
      text_align: 'text-center',
      font_size: '24px',
      font_weight: '800',
    },
  },
  buttons: [],
}

export function SurveyCompletion({
  onGoHome,
  homeUrl = '/',
}: SurveyCompletionProps) {
  const router = useRouter()

  // Scroll to top when completion page is shown
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [])

  const handleGoHome = () => {
    if (onGoHome) {
      onGoHome()
    } else {
      router.push(homeUrl)
    }
  }

  return (
    <main className="flex flex-col items-center gap-10">
      {/* Heading Section */}
      <HeadingSection heading={completionHeading} />

      {/* Content Card */}
      <div className="w-full bg-gray-50 rounded-xl p-6 sm:p-10 border border-gray-20 text-gray-800 space-y-6 sm:space-y-10">
        <div className="text-content">
          <Typography
            variant="body1"
            className="font-semibold text-gray-800 mb-2"
          >
            Cảm ơn Quý khách hàng đã hoàn thành khảo sát!
          </Typography>
          <Typography variant="body1" className="text-gray-800 mb-2">
            Phú Thái Cat trân trọng cảm ơn Quý khách hàng đã dành thời gian đóng
            góp ý kiến cho chúng tôi. Những phản hồi quý báu của Quý khách sẽ là
            cơ sở quan trọng giúp Phú Thái Cat không ngừng cải tiến và nâng cao
            chất lượng dịch vụ. Chúng tôi sẽ sớm liên hệ để gửi tặng phần quà
            tri ân đến Quý khách hàng.
          </Typography>
          <Typography variant="body1" className="text-gray-800">
            Trong trường hợp có nội dung cần làm rõ, nhân viên phụ trách của Phú
            Thái Cat có thể sẽ liên hệ trực tiếp với Quý khách trong thời gian
            tới. Một lần nữa, xin chân thành cảm ơn sự tin tưởng và đồng hành
            của Quý khách đối với Phú Thái Cat.
          </Typography>
        </div>

        {/* Home Button */}
        <div className="flex justify-center">
          <MainButton
            label="QUAY VỀ TRANG CHỦ"
            variant="secondary"
            onClick={handleGoHome}
            CustomIcon={ArrowRight}
            iconPosition="right"
          />
        </div>
      </div>
    </main>
  )
}

export default SurveyCompletion
