'use client'

import { Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import React from 'react'
import { useSurveyData } from '../hooks/use-survey-data'
import { useSurveyValidation } from '../hooks/use-survey-validation'
import { SurveyQuestion } from '../types/survey.types'
import DropdownQuestion from './questions/dropdown-question'
import MultipleChoiceQuestion from './questions/multiple-choice-question'
import OpenEndedQuestion from './questions/open-ended-question'
import RatingQuestion from './questions/rating-question'

interface QuestionRendererProps {
  question: SurveyQuestion
  index?: number
  className?: string
  isFollowUp?: boolean
}

export default function QuestionRenderer({
  question,
  index = 0,
  className = '',
  isFollowUp = false,
}: QuestionRendererProps) {
  const { getQuestionValue, setQuestionValue, shouldShowFollowUp } =
    useSurveyData()
  const { getQuestionError } = useSurveyValidation()

  const value = getQuestionValue(question)
  const error = getQuestionError(question.id)
  const showFollowUp = shouldShowFollowUp(question.id)

  const handleValueChange = (newValue: any) => {
    setQuestionValue(question, newValue)
  }

  const renderQuestion = () => {
    const commonProps = {
      question,
      value,
      onChange: handleValueChange,
      error,
    }

    switch (question.type) {
      case 'open-ended':
        return <OpenEndedQuestion {...commonProps} question={question} />

      case 'multiple-choice':
        return <MultipleChoiceQuestion {...commonProps} question={question} />

      case 'dropdown':
        return <DropdownQuestion {...commonProps} question={question} />

      case 'rating':
        return <RatingQuestion {...commonProps} question={question} />

      default:
        console.warn(`Unknown question type: ${(question as any).type}`)
        return (
          <div className="p-4 border border-red-300 rounded bg-red-50">
            <p className="text-red-600 text-sm">
              Unsupported question type: {(question as any).type}
            </p>
          </div>
        )
    }
  }

  const renderFollowUpQuestion = () => {
    if (!showFollowUp || question.type !== 'rating') return null

    const ratingQuestion = question as any // Type assertion for rating question
    if (!ratingQuestion.followUpQuestion) return null

    return (
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: 0.3 }}
      >
        <QuestionRenderer
          question={ratingQuestion.followUpQuestion}
          index={index}
          isFollowUp={true}
        />
      </motion.div>
    )
  }

  return (
    <motion.div
      id={`question-${question.id}`}
      className={`space-y-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.3,
        delay: index * 0.1,
      }}
    >
      {/* Question Title */}
      <div className="mb-4">
        <Typography variant="h6" className="font-semibold mb-2">
          {!isFollowUp && `${Math.floor(index + 1)}. `}
          {question.title}
          {question.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Typography>
        {question.description && (
          <Typography variant="caption" className="text-muted-foreground">
            {question.description}
          </Typography>
        )}
      </div>

      {/* Question Component */}
      {renderQuestion()}

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          <Typography variant="caption" className="text-destructive mt-2 block">
            {error}
          </Typography>
        </motion.div>
      )}

      {/* Follow-up Question */}
      {renderFollowUpQuestion()}
    </motion.div>
  )
}

// Utility component for rendering multiple questions
interface QuestionsListProps {
  questions: SurveyQuestion[]
  className?: string
}

export function QuestionsList({
  questions,
  className = '',
}: QuestionsListProps) {
  return (
    <div className={`space-y-10 ${className}`}>
      {questions.map((question, index) => (
        <QuestionRenderer key={question.id} question={question} index={index} />
      ))}
    </div>
  )
}

// Higher-order component for question validation
interface ValidatedQuestionRendererProps extends QuestionRendererProps {
  onValidationChange?: (isValid: boolean) => void
}

export function ValidatedQuestionRenderer({
  onValidationChange,
  ...props
}: ValidatedQuestionRendererProps) {
  const { validateQuestion } = useSurveyValidation()
  const { getQuestionValue } = useSurveyData()

  React.useEffect(() => {
    if (onValidationChange) {
      const value = getQuestionValue(props.question)
      const validation = validateQuestion(props.question, value)
      onValidationChange(validation.isValid)
    }
  }, [props.question, onValidationChange, validateQuestion, getQuestionValue])

  return <QuestionRenderer {...props} />
}

// Export question components for direct use
export {
  DropdownQuestion,
  MultipleChoiceQuestion,
  OpenEndedQuestion,
  RatingQuestion,
}
