'use client'

import HeadingSection from '~/src/components/renderer/heading-section'
import { SurveyProvider, useSurveyContext } from '../context/survey-context'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import {
  mockSurveyConfig,
  mockSurveyHeading,
} from '../services/survey-mock-data'
import { SurveyConfig } from '../types/survey.types'
import { MultiStepRenderer } from './dynamic-step-renderer'
import ProgressBar from './progress-bar'
import { SurveyCompletion } from './survey-completion'

interface CustomerSatisfactionSurveyProps {
  surveyConfig?: SurveyConfig
}

function SurveyContent({ surveyConfig }: { surveyConfig: SurveyConfig }) {
  const { currentStepIndex } = useSurveyNavigation()
  const { state } = useSurveyContext()

  // Show completion page if survey is completed
  if (state.isCompleted) {
    return <SurveyCompletion />
  }

  return (
    <div className="flex flex-col gap-4 sm:gap-10">
      {/* Progress Bar */}
      <ProgressBar showStepTitles={true} />

      <HeadingSection heading={mockSurveyHeading} />

      {/* Dynamic Step Renderer */}
      <MultiStepRenderer
        steps={surveyConfig.steps}
        currentStepIndex={currentStepIndex}
      />
    </div>
  )
}

export default function CustomerSatisfactionSurvey({
  surveyConfig = mockSurveyConfig,
}: CustomerSatisfactionSurveyProps) {
  return (
    <SurveyProvider initialConfig={surveyConfig}>
      <SurveyContent surveyConfig={surveyConfig} />
    </SurveyProvider>
  )
}
