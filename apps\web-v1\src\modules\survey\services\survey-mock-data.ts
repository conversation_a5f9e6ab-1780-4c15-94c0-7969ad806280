import { STEP_TYPES } from '../constants/survey.constants'
import { SurveyConfig } from '../types/survey.types'

export const mockSurveyHeading = {
  type: 'default',
  heading: {
    text: 'KHẢO SÁT MỨC ĐỘ HÀI LÒNG CỦA KHÁCH HÀNG',
    styles: {
      color: '#1F2937', // gray-800
      text_align: 'text-center',
      font_size: '24px',
      font_weight: '800',
    },
  },
  // sub_heading: {
  //   text: 'Khảo sát mức độ hài lòng của khách hàng trong giao dịch với Phú Thái Cat',
  //   styles: {
  //     color: '#6B7280', // gray-500
  //     text_align: 'text-center',
  //     font_size: '16px',
  //     font_weight: '400',
  //   },
  // },
  buttons: [],
}

// Mock survey data
export const mockSurveyConfig: SurveyConfig = {
  id: 'customer-satisfaction-survey',
  title: 'KHẢO SÁT MỨC ĐỘ HÀI LÒNG CỦA KHÁCH HÀNG',
  description:
    'Khảo sát mức độ hài lòng của khách hàng trong giao dịch với Phú Thái Cat',
  steps: [
    {
      id: 'policy-step',
      title: 'KHẢO SÁT MỨC ĐỘ HÀI LÒNG CỦA KHÁCH HÀNG',
      subtitle: 'Chính sách khảo sát',
      type: STEP_TYPES.POLICY,
      order: 1,
      content: JSON.stringify({
        infoBox: {
          title:
            'Cảm ơn Quý khách hàng đã tin tưởng lựa chọn Dịch vụ hỗ trợ sản phẩm của Phú Thái Cat.',
          description: `<div>
            <div>Với mục đích liên tục cải thiện chất lượng dịch vụ để phục vụ Quý khách hàng ngày càng tốt hơn, Phú Thái Cat kính mời quý khách dành ra <strong>3 phút</strong> để đánh giá và phản hồi về mức độ hài lòng của quý khách khi giao dịch với chúng tôi</div>
            <div>Phú Thái Cat sẽ <strong>liên hệ để gửi tặng một phần quà</strong> sau khi Quý khách hàng hoàn thành khảo sát này.</div>
            </div>`,
        },
        policy: {
          title: 'CHÍNH SÁCH KHẢO SÁT',
          content: [
            'Khảo sát này hoàn toàn tuân theo Bộ Luật Dân sự Việt Nam 2015.',
            'Những thông tin do khách hàng cung cấp sẽ được chia sẻ với Phú Thái Cat, Caterpillar và công ty nghiên cứu thị trường GCOMM. Phú Thái Cat cam kết bảo mật tuyệt đối mọi thông tin của Quý khách hàng và chỉ sử dụng vào mục đích nghiên cứu.',
            'Sau khảo sát này, có thể nhân viên phụ trách của Phú Thái Cat sẽ liên hệ với Quý khách để giải quyết những khúc mắc (nếu có).',
            'Quý khách hàng có đồng ý tham gia Khảo sát về mức độ hài lòng trong giao dịch với Phú Thái Cat không?',
          ],
        },
      }),
    },
    {
      id: 'confirmation-step',
      title: 'KHẢO SÁT MỨC ĐỘ HÀI LÒNG CỦA KHÁCH HÀNG',
      subtitle: 'Thông tin liên hệ',
      type: STEP_TYPES.CONFIRMATION,
      order: 2,
      content: JSON.stringify({
        infoText: {
          title: 'Thông tin liên hệ',
          description: [
            'Quý khách hàng vui lòng kiểm tra lại các thông tin liên hệ và có thể chỉnh sửa lại nếu chưa chính xác.',
            'Phú Thái Cat sẽ dựa vào những thông tin này để liên hệ với quý khách hàng và gửi quà tặng sau khi hoàn thành khảo sát.',
          ],
        },
        sections: [
          {
            id: 'contact_info',
            title: 'THÔNG TIN CÔNG TY VÀ NGƯỜI PHỤ TRÁCH',
            type: 'form',
          },
          {
            id: 'transaction_info',
            title: 'THÔNG TIN GIAO DỊCH',
            type: 'readonly',
            fields: [
              {
                id: 'transaction_type',
                label: 'Loại giao dịch',
                value: 'Sửa chữa tại công trường',
                type: 'text',
              },
              {
                id: 'invoice_number',
                label: 'Số hóa đơn',
                value: '3W09362',
                type: 'text',
              },
              {
                id: 'model',
                label: 'Model',
                value: '329D',
                type: 'text',
              },
              {
                id: 'serial',
                label: 'Serial',
                value: 'MNB00195',
                type: 'text',
              },
              {
                id: 'description',
                label: 'Mô tả giao dịch',
                value: 'KIỂM TRA VÀ ĐƯA RA KHUYẾN NGHỊ BẢO DƯỠNG, SỬA CHỮA',
                type: 'textarea',
              },
            ],
          },
        ],
      }),
      questions: [
        {
          id: 'contact_name',
          type: 'open-ended',
          title: 'Họ tên người phụ trách',
          required: true,
          order: 1,
          placeholder: 'Nhập họ tên...',
        },
        {
          id: 'contact_phone',
          type: 'open-ended',
          title: 'Số điện thoại người phụ trách',
          required: true,
          order: 2,
          placeholder: 'Nhập số điện thoại...',
        },
        {
          id: 'contact_email',
          type: 'open-ended',
          title: 'Email người phụ trách',
          required: true,
          order: 3,
          placeholder: 'Nhập email...',
        },
        {
          id: 'company_name',
          type: 'open-ended',
          title: 'Tên công ty',
          required: true,
          order: 4,
          placeholder: 'Nhập tên công ty...',
        },
        {
          id: 'company_address',
          type: 'open-ended',
          title: 'Địa chỉ công ty',
          required: true,
          order: 5,
          placeholder: 'Nhập địa chỉ...',
        },
        {
          id: 'company_tax_code',
          type: 'open-ended',
          title: 'Mã số thuế',
          required: false,
          order: 6,
          placeholder: 'Nhập mã số thuế...',
        },
      ],
    },
    {
      id: 'questions-step',
      title: 'KHẢO SÁT MỨC ĐỘ HÀI LÒNG CỦA KHÁCH HÀNG',
      subtitle: 'Câu hỏi khảo sát',
      type: STEP_TYPES.QUESTIONS,
      order: 3,
      questions: [
        {
          id: 'overall_satisfaction',
          type: 'rating',
          title:
            'Nhìn chung, Quý khách có hài lòng với Phú Thái Cat trong công việc sửa chữa tại công trường không?',
          required: true,
          order: 1,
          minValue: 1,
          maxValue: 10,
          showFollowUpQuestion: false, // Question 1 doesn't show follow-up
          excludeFromFollowUp: true,
        },
        {
          id: 'service_continuation',
          type: 'rating',
          title:
            'Khi có nhu cầu sửa chữa tại công trường, Quý khách có ý định tiếp tục sử dụng dịch vụ của Phú Thái Cat?',
          required: true,
          order: 2,
          minValue: 1,
          maxValue: 10,
          showFollowUpQuestion: true,
          followUpThreshold: 7,
          followUpQuestion: {
            id: 'service_continuation_feedback',
            type: 'open-ended',
            title:
              'Vui lòng cho biết lý do tại sao Quý khách đánh giá như vậy?',
            required: true,
            order: 2.1,
            placeholder: 'Chia sẻ ý kiến của bạn...',
            maxLength: 500,
          },
        },
        {
          id: 'response_time_satisfaction',
          type: 'rating',
          title:
            'Quý khách có hài lòng về thời gian tính từ cuộc gọi đầu tiên của quý khách tới Phú Thái Cat đến khi kỹ thuật viên của chúng tôi tới công trường?',
          required: true,
          order: 3,
          minValue: 1,
          maxValue: 10,
          showFollowUpQuestion: true,
          followUpThreshold: 7,
          followUpQuestion: {
            id: 'response_time_feedback',
            type: 'open-ended',
            title: 'Vui lòng chia sẻ thêm về thời gian phản hồi?',
            required: true,
            order: 3.1,
            placeholder: 'Chia sẻ ý kiến của bạn...',
            maxLength: 500,
          },
        },
        {
          id: 'technician_communication',
          type: 'rating',
          title:
            'Quý khách có hài lòng với cách giao tiếp của kỹ thuật viên Phú Thái Cat tại công trường trong khi sửa chữa không?',
          required: true,
          order: 4,
          minValue: 1,
          maxValue: 10,
          showFollowUpQuestion: true,
          followUpThreshold: 7,
          followUpQuestion: {
            id: 'technician_communication_feedback',
            type: 'open-ended',
            title: 'Vui lòng chia sẻ thêm về trải nghiệm với kỹ thuật viên?',
            required: true,
            order: 4.1,
            placeholder: 'Chia sẻ ý kiến của bạn...',
            maxLength: 500,
          },
        },
        {
          id: 'improvement_suggestions',
          type: 'open-ended',
          title:
            'Theo Quý khách, Phú Thái Cat nên làm gì để khách hàng hài lòng hơn về việc hoàn thành sửa chữa đúng như cam kết?',
          required: false,
          order: 5,
          placeholder: 'Chia sẻ gợi ý của bạn...',
          maxLength: 1000,
        },
        {
          id: 'service_type_preference',
          type: 'multiple-choice',
          title: 'Quý khách thường sử dụng loại dịch vụ nào của Phú Thái Cat?',
          required: true,
          order: 5.1,
          allowMultiple: true,
          options: [
            {
              id: 'maintenance',
              label: 'Bảo trì định kỳ',
              value: 'maintenance',
            },
            {
              id: 'emergency_repair',
              label: 'Sửa chữa khẩn cấp',
              value: 'emergency_repair',
            },
            {
              id: 'parts_replacement',
              label: 'Thay thế phụ tùng',
              value: 'parts_replacement',
            },
            {
              id: 'consultation',
              label: 'Tư vấn kỹ thuật',
              value: 'consultation',
            },
            {
              id: 'training',
              label: 'Đào tạo vận hành',
              value: 'training',
            },
          ],
          hasOtherOption: true,
        },
        {
          id: 'contact_method_preference',
          type: 'dropdown',
          title: 'Quý khách muốn được liên hệ qua phương thức nào?',
          required: true,
          order: 5.2,
          options: [
            {
              id: 'phone',
              label: 'Điện thoại',
              value: 'phone',
            },
            {
              id: 'email',
              label: 'Email',
              value: 'email',
            },
            {
              id: 'zalo',
              label: 'Zalo',
              value: 'zalo',
            },
            {
              id: 'teams',
              label: 'Microsoft Teams',
              value: 'teams',
            },
            {
              id: 'in_person',
              label: 'Gặp trực tiếp',
              value: 'in_person',
            },
          ],
          hasOtherOption: true,
        },
        {
          id: 'service_frequency',
          type: 'multiple-choice',
          title:
            'Quý khách sử dụng dịch vụ của Phú Thái Cat với tần suất như thế nào?',
          required: true,
          order: 5.3,
          allowMultiple: false,
          options: [
            {
              id: 'weekly',
              label: 'Hàng tuần',
              value: 'weekly',
            },
            {
              id: 'monthly',
              label: 'Hàng tháng',
              value: 'monthly',
            },
            {
              id: 'quarterly',
              label: 'Hàng quý',
              value: 'quarterly',
            },
            {
              id: 'yearly',
              label: 'Hàng năm',
              value: 'yearly',
            },
            {
              id: 'as_needed',
              label: 'Khi có nhu cầu',
              value: 'as_needed',
            },
          ],
        },
        {
          id: 'equipment_satisfaction',
          type: 'rating',
          title:
            'Quý khách có hài lòng về các trang thiết bị và phụ tùng mà kỹ thuật viên Phú Thái Cat mang tới công trường để thực hiện công việc sửa chữa không?',
          required: true,
          order: 6,
          minValue: 1,
          maxValue: 10,
          showFollowUpQuestion: true,
          followUpThreshold: 7,
          followUpQuestion: {
            id: 'equipment_feedback',
            type: 'open-ended',
            title: 'Vui lòng chia sẻ thêm về trang thiết bị và phụ tùng?',
            required: true,
            order: 6.1,
            placeholder: 'Chia sẻ ý kiến của bạn...',
            maxLength: 500,
          },
        },
        {
          id: 'completion_time_satisfaction',
          type: 'rating',
          title:
            'Quý khách có hài lòng về thời gian thực tế hoàn thành dịch vụ sửa chữa tại công trường so với cam kết của Phú Thái Cat?',
          required: true,
          order: 7,
          minValue: 1,
          maxValue: 10,
          showFollowUpQuestion: true,
          followUpThreshold: 7,
          followUpQuestion: {
            id: 'completion_time_feedback',
            type: 'open-ended',
            title: 'Vui lòng chia sẻ thêm về thời gian hoàn thành?',
            required: true,
            order: 7.1,
            placeholder: 'Chia sẻ ý kiến của bạn...',
            maxLength: 500,
          },
        },
        {
          id: 'repair_quality_satisfaction',
          type: 'rating',
          title:
            'Quý khách có hài lòng về chất lượng sửa chữa của kỹ thuật viên Phú Thái Cat khắc phục sự cố máy ngay trong lần đầu đến công trường?',
          required: true,
          order: 8,
          minValue: 1,
          maxValue: 10,
          showFollowUpQuestion: true,
          followUpThreshold: 7,
          followUpQuestion: {
            id: 'repair_quality_feedback',
            type: 'open-ended',
            title: 'Vui lòng chia sẻ thêm về chất lượng sửa chữa?',
            required: true,
            order: 8.1,
            placeholder: 'Chia sẻ ý kiến của bạn...',
            maxLength: 500,
          },
        },
        {
          id: 'general_feedback',
          type: 'open-ended',
          title:
            'Quý khách có nhận xét hoặc gợi ý nào khác để giúp Phú Thái Cat cải thiện dịch vụ sửa chữa tại công trường?',
          required: false,
          order: 9,
          placeholder: 'Chia sẻ ý kiến của bạn...',
          maxLength: 1000,
        },
      ],
    },
  ],
  settings: {
    allowBackNavigation: true,
    showProgressBar: true,
    autoSave: true,
    submitOnComplete: true,
    // redirectOnComplete: '/', // Commented out to show completion page instead
  },
}

// Mock service để fetch survey config
export const fetchSurveyConfig = async (
  surveyId: string,
): Promise<SurveyConfig> => {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  if (surveyId === 'customer-satisfaction-survey') {
    return mockSurveyConfig
  }

  throw new Error(`Survey with ID ${surveyId} not found`)
}

// Mock service submit survey
export const submitSurveyResponse = async (
  surveyId: string,
  responses: any,
): Promise<void> => {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  console.log('Survey submitted:', { surveyId, responses })

  // Simulate success
  return Promise.resolve()
}
