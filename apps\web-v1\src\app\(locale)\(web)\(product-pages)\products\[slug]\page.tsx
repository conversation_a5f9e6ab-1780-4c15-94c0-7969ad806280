import { Metadata } from 'next'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { PageBreadcrumb } from '~/src/modules/layout'
import ProductModelDetailView from '~/src/modules/product/product-model-detail-view'
import ProductsView from '~/src/modules/product/products-view'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function ProductSlugPage(props: {
  params: Promise<{ slug: string }>
}) {
  const params = await props.params
  const slug = params.slug

  const locale = await getLocale()

  const productExists = await fetchContentType({
    contentType: apiRoute.products,
    params: {
      filters: { locale, slug },
      fields: ['id', 'slug', 'name'],
      populate: false,
    },
    spreadData: true,
  })

  if (productExists) {
    const productData = await fetchContentType({
      contentType: apiRoute.products,
      params: {
        filters: { locale, slug },
      },
      spreadData: true,
    })

    const pageData = await fetchContentType({
      contentType: apiRoute.pageProductSingle,
      params: {
        filters: { locale },
      },
      spreadData: true,
    })

    const localizedSlugs = pageData?.localizations?.reduce(
      (acc: Record<string, string>, localization: any) => {
        acc[localization.locale] = slug
        return acc
      },
      { [locale]: slug },
    )

    const dynamicZones = productData?.dynamic_zone || []
    console.log('🚀 ~ ProductSlugPage ~ productData:', productData)

    const pageHeroSection = dynamicZones.find(
      (zone: any) => zone.__component === 'dynamic-zone.page-hero-section',
    )

    const heroSectionData = pageHeroSection || {
      __component: 'dynamic-zone.page-hero-section',
      id: 'auto-generated-hero',
      heading: {
        heading: {
          text: productData?.name || productData?.title || 'Product Details',
        },
        sub_heading: {
          text:
            productData?.description || productData?.short_description || '',
        },
        buttons: [],
      },
      image: {
        image:
          productData?.image_gallery?.[0] ||
          productData?.image ||
          productData?.images?.[0] ||
          null,
      },
      showDefaultButtons: true,
    }

    const otherDynamicZones = dynamicZones.filter(
      (zone: any) => zone.__component !== 'dynamic-zone.page-hero-section',
    )

    return (
      <>
        <ClientSlugHandler localizedSlugs={localizedSlugs} />
        <PageBreadcrumb
          items={[
            { title: 'Products', href: '/product-categories' },
            { title: productData?.name || productData?.title },
          ]}
        />

        <PageContent
          pageData={{
            ...pageData,
            dynamic_zone: [heroSectionData],
          }}
        />

        <ProductsView />

        {otherDynamicZones.length > 0 && (
          <PageContent
            pageData={{
              ...pageData,
              dynamic_zone: otherDynamicZones,
            }}
          />
        )}
      </>
    )
  }

  const modelExists = await fetchContentType({
    contentType: 'product-models',
    params: {
      filters: { locale, slug },
      fields: ['id', 'slug', 'name'],
      populate: false,
    },
    spreadData: true,
  })

  if (modelExists) {
    const modelData = await fetchContentType({
      contentType: apiRoute.productModels,
      params: {
        filters: { locale, slug },
      },
      spreadData: true,
    })
    const pageData = await fetchContentType({
      contentType: apiRoute.pageProductSingle,
      params: {
        filters: {
          slug,
          locale,
        },
      },
    })

    const localizedSlugs = pageData?.localizations?.reduce(
      (acc: Record<string, string>, localization: any) => {
        acc[localization.locale] = slug
        return acc
      },
      { [locale]: slug },
    )

    return (
      <>
        <ClientSlugHandler localizedSlugs={localizedSlugs} />
        <PageBreadcrumb
          items={[
            { title: 'Products', href: '/product-categories' },
            {
              title: modelData.product?.name || modelData.product?.title,
              href: `/products/${modelData.product?.slug}`,
            },
            { title: modelData?.name || modelData?.title },
          ]}
        />
        <ProductModelDetailView productModelData={modelData} />
        {/* <PageContent pageData={modelData} /> */}
      </>
    )
  }
}
