'use client'

import { LINGUI_CONFIG } from '~/src/config-global'
import StylesSection from '../renderer/styles-section'
import MediaStackSection from './cms-sections/media-stack-section'

interface TStackedCardsProps {
  __component: string
  id: number
  styles?: any
  media_text_cards: {
    id: number
    gap: number
    cards: any[]
  }
  locale?: string
}

export default function StackedCardsSection({
  styles,
  media_text_cards,
  locale = LINGUI_CONFIG.defaultLocale,
}: TStackedCardsProps) {
  const { gap = 40, cards = [] } = media_text_cards || {}

  const renderContent = (
    <div
      className="flex flex-col"
      style={{
        gap: `${gap}px`,
      }}
    >
      {cards.map((card, index) => {
        // Transform card data to MediaTextSection format
        const mediaTextData = {
          ...card,
          locale,
          isStack: true,
          layout: 'large_media',
          image: card.image
            ? {
                image: card.image,
                alt: card.image.alternativeText || card.image.name || '',
                border_radius: 24,
              }
            : undefined,
          container_styles: card.background || undefined,
          // Keep original styles if needed
          styles: card.background
            ? {
                background: card.background,
              }
            : undefined,
        }

        return (
          <section
            key={card.id || index}
            className="min-h-[60vh] w-full sticky top-0"
          >
            <MediaStackSection {...mediaTextData} />
          </section>
        )
      })}
    </div>
  )

  // If no content_max_width or not full_width, wrap with StylesSection
  if (styles?.content_max_width !== 'full_width') {
    return <StylesSection styles={styles}>{renderContent}</StylesSection>
  }

  // For full_width, render directly
  return renderContent
}
