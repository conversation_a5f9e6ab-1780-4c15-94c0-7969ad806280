{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Pages", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "title", "required": true}, "template": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": false}}, "default": "dynamic", "enum": ["dynamic", "home-page", "blogs-page", "products-page"]}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.testimonials-section", "dynamic-zone.media-text-section", "dynamic-zone.latest-news-section", "dynamic-zone.hero-slide-section", "dynamic-zone.accordion-section", "dynamic-zone.featured-promotions", "dynamic-zone.hover-overlay-card-collection", "dynamic-zone.hover-expand-card-collection", "dynamic-zone.form-subscribe", "dynamic-zone.video-section", "dynamic-zone.page-hero-section", "dynamic-zone.executive-team-section", "dynamic-zone.locations-section", "dynamic-zone.image-box-section", "dynamic-zone.contact-us-section", "dynamic-zone.related-products", "dynamic-zone.stats-section", "dynamic-zone.features-section", "dynamic-zone.image-slider-section", "dynamic-zone.job-listings-section", "dynamic-zone.related-articles", "dynamic-zone.service-list-section", "dynamic-zone.support-center-section", "dynamic-zone.partners-commitment-section", "dynamic-zone.solution-portfolio-section", "dynamic-zone.product-categories-section", "dynamic-zone.latest-promotions-section", "dynamic-zone.step-guide-section", "dynamic-zone.solutions-by-application", "dynamic-zone.related-product-categories", "dynamic-zone.icon-box-list-section", "dynamic-zone.solution-categories-section", "dynamic-zone.distributed-brands", "dynamic-zone.info-block", "dynamic-zone.timeline-section", "dynamic-zone.widget", "product-parts.product-part-categories", "products.specification-item", "products.products-recent-view", "products.products-by-category", "products.product-categories", "dynamic-zone.media-gallery-section", "dynamic-zone.policies-section", "dynamic-zone.toggle-card-slider", "dynamic-zone.stacked-cards"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "page_slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "title"}}}