import React from 'react'

interface PencilIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
  className?: string
}

export const PencilIcon: React.FC<PencilIconProps> = ({
  size = 24,
  className,
}) => {
  const computedHeight = (size / 25) * 24
  return (
    <svg
      width={size}
      height={computedHeight}
      viewBox="0 0 25 24"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.13433 15.951C5.27085 15.5961 5.33911 15.4186 5.42823 15.2526C5.5074 15.1051 5.59877 14.9645 5.70139 14.8323C5.81692 14.6834 5.95138 14.5489 6.22028 14.28L17.5 3.0003C18.6046 1.89573 20.3955 1.89573 21.5 3.0003C22.6046 4.10487 22.6046 5.89574 21.5 7.0003L10.2203 18.28C9.95137 18.5489 9.81692 18.6834 9.66804 18.7989C9.5358 18.9015 9.3952 18.9929 9.24772 19.0721C9.08168 19.1612 8.90421 19.2295 8.54927 19.366L3 21.5003L5.13433 15.951Z"
        fill="#182230"
      />
      <path
        d="M5.05812 16.1493C5.2054 15.7663 5.27903 15.5749 5.40534 15.4872C5.51572 15.4105 5.6523 15.3816 5.7843 15.4068C5.93533 15.4356 6.08038 15.5807 6.37048 15.8708L8.62957 18.1299C8.91967 18.42 9.06472 18.565 9.09356 18.716C9.11877 18.848 9.08979 18.9846 9.01314 19.095C8.92545 19.2213 8.73399 19.2949 8.35107 19.4422L3 21.5003L5.05812 16.1493Z"
        fill="#182230"
      />
      <path
        d="M3 21.5003L8.54927 19.366C8.90421 19.2295 9.08168 19.1612 9.24772 19.0721C9.3952 18.9929 9.5358 18.9015 9.66804 18.7989C9.81692 18.6834 9.95137 18.5489 10.2203 18.28L21.5 7.0003C22.6046 5.89574 22.6046 4.10487 21.5 3.0003C20.3955 1.89573 18.6046 1.89573 17.5 3.0003L6.22028 14.28C5.95138 14.5489 5.81692 14.6834 5.70139 14.8323C5.59877 14.9645 5.5074 15.1051 5.42823 15.2526C5.33911 15.4186 5.27085 15.5961 5.13433 15.951L3 21.5003ZM3 21.5003L5.05812 16.1493C5.2054 15.7663 5.27903 15.5749 5.40534 15.4872C5.51572 15.4105 5.6523 15.3816 5.7843 15.4068C5.93533 15.4356 6.08038 15.5807 6.37048 15.8708L8.62957 18.1299C8.91967 18.4199 9.06472 18.565 9.09356 18.716C9.11877 18.848 9.08979 18.9846 9.01314 19.095C8.92545 19.2213 8.73399 19.2949 8.35107 19.4422L3 21.5003Z"
        stroke="#182230"
        strokeWidth="1.7"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
