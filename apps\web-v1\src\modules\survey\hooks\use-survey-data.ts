import { useCallback } from 'react'
import { RATING_SCALE } from '../constants/survey.constants'
import { useSurveyContext } from '../context/survey-context'
import {
  RatingQuestion,
  SurveyFormData,
  SurveyQuestion,
} from '../types/survey.types'

export interface SurveyDataHook {
  formData: SurveyFormData
  getAnswer: (questionId: string) => any
  setAnswer: (questionId: string, value: any) => void
  getQuestionValue: <T extends SurveyQuestion>(question: T) => any
  setQuestionValue: <T extends SurveyQuestion>(question: T, value: any) => void
  isQuestionAnswered: (questionId: string) => boolean
  getStepCompletion: (stepId: string) => number
  getCurrentStepCompletion: () => number
  shouldShowFollowUp: (questionId: string) => boolean
  toggleFollowUp: (questionId: string, show: boolean) => void
  clearAnswer: (questionId: string) => void
  clearStepAnswers: (stepId: string) => void
  getFormattedAnswers: () => Record<string, any>
}

export function useSurveyData(): SurveyDataHook {
  const { state, updateAnswer, dispatch, getCurrentStep } = useSurveyContext()

  const formData = state.formData

  const getAnswer = useCallback(
    (questionId: string): any => {
      return formData[questionId]
    },
    [formData],
  )

  const setAnswer = useCallback(
    (questionId: string, value: any) => {
      updateAnswer(questionId, value)

      // Handle rating question follow-up logic
      const currentStep = getCurrentStep()
      if (currentStep?.questions) {
        const question = currentStep.questions.find((q) => q.id === questionId)
        if (question?.type === 'rating') {
          const ratingQuestion = question as RatingQuestion
          if (
            ratingQuestion.showFollowUpQuestion &&
            !ratingQuestion.excludeFromFollowUp
          ) {
            const threshold =
              ratingQuestion.followUpThreshold || RATING_SCALE.followUpThreshold
            const shouldShow = typeof value === 'number' && value <= threshold
            dispatch({
              type: 'TOGGLE_FOLLOW_UP',
              payload: { questionId, show: shouldShow },
            })
          }
        }
      }
    },
    [updateAnswer, getCurrentStep, dispatch],
  )

  const getQuestionValue = useCallback(
    <T extends SurveyQuestion>(question: T): any => {
      return getAnswer(question.id)
    },
    [getAnswer],
  )

  const setQuestionValue = useCallback(
    <T extends SurveyQuestion>(question: T, value: any) => {
      setAnswer(question.id, value)
    },
    [setAnswer],
  )

  const isQuestionAnswered = useCallback(
    (questionId: string): boolean => {
      const value = getAnswer(questionId)
      if (value === null || value === undefined || value === '') {
        return false
      }
      if (Array.isArray(value)) {
        return value.length > 0
      }
      return true
    },
    [getAnswer],
  )

  const getStepCompletion = useCallback(
    (stepId: string): number => {
      if (!state.config) return 0

      const step = state.config.steps.find((s) => s.id === stepId)
      if (!step?.questions || step.questions.length === 0) {
        return 100 // Steps without questions are considered complete
      }

      const answeredQuestions = step.questions.filter((q) =>
        isQuestionAnswered(q.id),
      )
      return (answeredQuestions.length / step.questions.length) * 100
    },
    [state.config, isQuestionAnswered],
  )

  const getCurrentStepCompletion = useCallback((): number => {
    const currentStep = getCurrentStep()
    if (!currentStep) return 0
    return getStepCompletion(currentStep.id)
  }, [getCurrentStep, getStepCompletion])

  const shouldShowFollowUp = useCallback(
    (questionId: string): boolean => {
      return state.followUpQuestions[questionId] || false
    },
    [state.followUpQuestions],
  )

  const toggleFollowUp = useCallback(
    (questionId: string, show: boolean) => {
      dispatch({
        type: 'TOGGLE_FOLLOW_UP',
        payload: { questionId, show },
      })
    },
    [dispatch],
  )

  const clearAnswer = useCallback(
    (questionId: string) => {
      updateAnswer(questionId, null)
    },
    [updateAnswer],
  )

  const clearStepAnswers = useCallback(
    (stepId: string) => {
      if (!state.config) return

      const step = state.config.steps.find((s) => s.id === stepId)
      if (step?.questions) {
        step.questions.forEach((question) => {
          clearAnswer(question.id)
        })
      }
    },
    [state.config, clearAnswer],
  )

  const getFormattedAnswers = useCallback((): Record<string, any> => {
    if (!state.config) return {}

    const formatted: Record<string, any> = {}

    state.config.steps.forEach((step) => {
      if (step.questions) {
        step.questions.forEach((question) => {
          const value = getAnswer(question.id)
          if (value !== null && value !== undefined && value !== '') {
            formatted[question.id] = {
              questionTitle: question.title,
              questionType: question.type,
              value: value,
              stepId: step.id,
              stepTitle: step.title,
            }
          }
        })
      }
    })

    return formatted
  }, [state.config, getAnswer])

  // Memoized computed values
  // const computedValues = useMemo(() => {
  //   const currentStep = getCurrentStep()
  //   const currentStepCompletion = getCurrentStepCompletion()

  //   return {
  //     currentStep,
  //     currentStepCompletion,
  //   }
  // }, [getCurrentStep, getCurrentStepCompletion])

  return {
    formData,
    getAnswer,
    setAnswer,
    getQuestionValue,
    setQuestionValue,
    isQuestionAnswered,
    getStepCompletion,
    getCurrentStepCompletion,
    shouldShowFollowUp,
    toggleFollowUp,
    clearAnswer,
    clearStepAnswers,
    getFormattedAnswers,
  }
}
