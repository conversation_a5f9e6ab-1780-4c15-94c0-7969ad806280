{"collectionName": "components_cards_media_text_cards", "info": {"displayName": "Media_Text_Card", "icon": "stack"}, "options": {}, "attributes": {"heading": {"type": "component", "component": "items.text-item", "repeatable": false}, "sub_heading": {"type": "component", "component": "items.text-item", "repeatable": false}, "description": {"type": "blocks"}, "buttons": {"type": "component", "component": "shared.button", "repeatable": true}, "distributed_brands": {"type": "relation", "relation": "oneToMany", "target": "api::brand.brand"}, "image": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "image_position": {"type": "enumeration", "default": "right", "enum": ["left", "right"]}, "layout": {"type": "enumeration", "enum": ["split", "large-media"]}, "background": {"type": "component", "component": "styles.background-styles", "repeatable": false}}, "config": {}}